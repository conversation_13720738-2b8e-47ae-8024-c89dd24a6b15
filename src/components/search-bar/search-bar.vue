<template>
  <section
    id="nav-border"
    class="search-bar"
    :class="{ 'search-bar-in-form': props.isInForm }"
    role="search"
  >
    <div class="search-bar-box" :class="{'search-bar-box-in-form': props.isInForm}">
      <button
        type="button"
        class="btn-reset search-bar-btn search-bar-btn-search"
        @click="handleSearch()"
        :disabled="!queryModel"
        data-test-id="search-icon"
      >
        <img src="@/assets/images/Search_Bar_Icon-web.png" alt="search icon" />
      </button>
      <input
        type="text"
        v-model.trim="queryModel"
        class="search-bar-input"
        :placeholder="getPlaceholder"
        @keyup.enter="handleSearch()"
        autocomplete="off"
        data-test-id="search-input"
      />
      <button
        type="button"
        class="btn-reset search-bar-btn search-bar-btn-clear"
        @click="handleSearch(true)"
        :disabled="!isSearchEnabled"
        data-test-id="exit-icon"
      >
        <img src="@/assets/images/exit_Swap_Model.svg?skipsvgo=true" alt="exit icon" width="13" height="13" />
      </button>
    </div>
  </section>
</template>
<script setup>
import { ref, computed, watch } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { QUERY_PARAM_KEY } from "../../сonstants/query-param-key.js";
import { useSearchStore } from "../../stores/search.js";

const props = defineProps({
  isInForm: {
    type: Boolean,
    default: false,
    required: false,
  },
  customPlaceholder: {
    type: String,
    default: "",
  },
  searchContext: {
    type: String,
    default: "global",
    required: false,
  },
  searchQueryParamKey: {
    type: String,
    default: QUERY_PARAM_KEY.SEARCH,
    required: false,
  },
});

const router = useRouter();
const route = useRoute();
const {
  searchQuery,
  isSearchEnabled,
  setSearchQuery,
} = useSearchStore();

const queryModel = ref("");
const placeholderMap = ref({
  "/recipes": "Search by recipe name, isin or external id",
  "/category": "Search by category name or isin",
  "/ingredients": "Search by ingredient name",
  "/cat-group": "Search by category group name or isin",
  "/tags": "Search by tag name or isin",
  "/iq-users": "Search by user email",
});

const getPlaceholder = computed(() => props.customPlaceholder || placeholderMap.value[route.path] || "Search here...");

const handleSearch = (clean = false) => {
  const value = clean ? "" : queryModel.value;
  const emitQueryParam = props.searchContext === 'global';
  setSearchQuery(value, { emitQueryParam, context: props.searchContext });
};

watch(searchQuery, ({ str, emitQueryParam }) => {
  if (str !== queryModel.value) {
    queryModel.value = str;
  }

  if (emitQueryParam) {
    router.push({
      query: {
        ...route.query,
        [QUERY_PARAM_KEY.PAGE]: undefined,
        [props.searchQueryParamKey]: str || undefined
      }
    })?.catch();
  }
});

watch(
  () => [route.query[props.searchQueryParamKey], route.query["editRecipeQuery"]],
  (data) => {
    if (props.searchContext !== 'global') return;
    const safeValue = data?.flatMap((item) => item)?.find((item) => !!item) || "";

    if (safeValue !== queryModel.value) {
      queryModel.value = safeValue;
      setSearchQuery(safeValue, { emitQueryParam: false, context: props.searchContext });
    }
  },
  { immediate: true },
);
</script>
