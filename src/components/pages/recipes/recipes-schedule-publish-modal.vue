<template>
  <div class="recipes-schedule-publish-modal font-family-averta">

    <div v-if="isSchedule" class="recipes-schedule-publish-modal-title text-h2">{{ $t('PAGE.RECIPES.MODAL.RECIPES_SCHEDULE_TITLE') }}</div>
    <div v-else class="recipes-schedule-publish-modal-title text-h2">
      <span>{{ $t('PAGE.RECIPES.MODAL.RECIPES_PUBLISH_TITLE') }}</span>
      <span class="simple-data-tooltip simple-data-tooltip-bottom" :data-tooltip-text="$t('PAGE.RECIPES.TOOLTIP.TEXT_RECIPES_PUBLISHED_INFO')">
        <img alt="info" src="@/assets/images/informationSymbol.png" width="14" height="14" />
      </span>
    </div>

    <div class="recipes-schedule-publish-modal-body">
      <div v-if="isSchedule" class="recipes-schedule-publish-modal-dates-section">
        <div class="text-title-2">{{ $t('PAGE.RECIPES.MODAL.RECIPES_CALENDAR_TITLE') }}</div>
        <CalendarPicker
          class="banner-datepicker-popup"
          v-model="range"
          :isRange="true"
          :isInRecipe="true"
          :startDate="recipeSchedulePublishDate"
          :endDate="recipeScheduleEndDate"
          @update:model-value="handleDateClickPopup"
        />
      </div>
      <div class="recipes-schedule-publish-modal-table-section w-100 iq-scrollbar">
        <simple-table
          v-if="rows.length"
          :column-keys="columnKeys"
          :column-names="columnNames"
          :data-source="rows"
          table-class="recipes-schedule-publish-modal-table"
        >
          <template v-slot:image="props">
            <img
              :src="props.data?.image || defaultImage"
              :src-placeholder="defaultImage"
              :alt="props.data?.title"
              class="simple-table-img"
              @error="$event.target.src = defaultImage"
            />
          </template>

          <template v-slot:isin="props">
            <span class="font-size-14 font-normal color-black">{{ props.data?.isin }}</span>
          </template>

          <template v-slot:title="props">
            <span class="font-size-14 font-bold color-black">{{ props.data?.title }}</span>
          </template>

          <template v-slot:modified="props">
            <span class="font-size-14 font-normal color-stone-gray">
              {{ props.data?.lastMod ? formatJsonTimestamp(props.data?.lastMod) : "" }}
            </span>
          </template>

          <template v-slot:externalId="props">
            <span class="font-size-14 font-normal color-stone-gray">
              {{ props.data?.externalId }}
            </span>
          </template>
        </simple-table>
      </div>
    </div>
    <div class="recipes-schedule-publish-modal-actions">
      <button
        type="button"
        class="btn-green-outline"
        @click="actionClose"
      >
        {{ $t('BUTTONS.CANCEL_BUTTON') }}
      </button>
      <button
        type="button"
        class="btn-green"
        :disabled="isSchedule && (!publishDate || !endDate) || (publishDate === endDate)"
        @click="actionSchedule"
      >
        {{ isSchedule ? $t('BUTTONS.SCHEDULE') : $t('BUTTONS.PUBLISH_BUTTON') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import CalendarPicker from "../../calendar-picker.vue";
import { ref } from "vue";
import { useI18n } from "vue-i18n";
import defaultImage from "~/assets/images/default_recipe_image.png";
import SimpleTable from "../../simple-table/simple-table.vue";

const props = defineProps({
  isSchedule: {
    type: Boolean,
  },
  rows: {
    type: Array,
    default: []
  },
});

const emit = defineEmits(["callback", "close"]);

const { t } = useI18n();
const {
  formatJsonTimestamp,
  convertToTimestamp,
} = useTimeUtils();

const range = ref({
  start: null,
  end: null,
});
const recipeSchedulePublishDate = ref("");
const recipeScheduleEndDate = ref("");
const publishDate = ref("");
const endDate = ref("");
const columnNames = ref(["", t('ISIN'), t('COMMON.RECIPE_TITLE'), t('MODIFIED'), t('EXTERNAL_ID')]);
const columnKeys = ref(['image', 'isin', 'title', 'modified', 'externalID']);

const handleDateClickPopup = (newValue) => {
  const publish = convertToTimestamp(newValue[0]).toString();
  const end = convertToTimestamp(newValue[1]).toString();
  recipeSchedulePublishDate.value = publish;
  recipeScheduleEndDate.value = end;
  publishDate.value = publish;
  endDate.value = end;
};

const actionClose = () => emit("close", false);
const actionSchedule = () => {
  if (props.isSchedule) {
    emit("close", {
      publishDate: publishDate.value,
      endDate: endDate.value,
    });
  } else {
    emit("close", true);
  }
}
</script>
