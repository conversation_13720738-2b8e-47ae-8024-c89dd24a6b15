<template>
  <div>
    <simple-content-wrapper
      class="add-recipes-section"
      v-if="
        !isEdit &&
        recipeDataForCategories.length === 0 &&
        categoryPromotedRecipes.length === 0
      "
    >
      <div class="add-recipes-content">
        <div class="add-recipes-left">
          <div class="add-recipes-header">
            <h2 class="text-h2 font-normal">
              {{ $t("CATEGORY.ADD_RECIPE_TO_CATEGORY") }}
            </h2>
            <p class="text-title-2 font-normal color-grey">
              {{ $t("CATEGORY.ADD_RECIPES_TO_NEW_CATEGORY") }}
            </p>
          </div>
          <button type="button" class="btn-green" @click="$emit('add-recipe-to-category')">
            {{ $t("COMMON.ADD_RECIPES") }}
          </button>
        </div>
        <div class="add-recipes-right">
          <div class="add-recipes-illustration">
            <img
              alt="Pan"
              class="pan-image"
              src="@/assets/images/pan-image.png"
            />
          </div>
        </div>
      </div>
    </simple-content-wrapper>

    <simple-content-wrapper
      class="content-recipes-combined-wrapper"
      v-if="
        isEdit ||
        (!isEdit &&
          (recipeDataForCategories.length > 0 ||
            categoryPromotedRecipes.length > 0))
      "
    >
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="category-recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ categoryPromotedRecipesTotal }}
                  {{
                    categoryPromotedRecipesTotal === 1
                      ? $t("CATEGORY.PROMOTED_RECIPE")
                      : $t("CATEGORY.PROMOTED_RECIPES")
                  }}
                </span>
                <div class="text-title-2 font-normal">
                  <span class="color-grey">{{
                    $t("CATEGORY.SHOW_CATEGORY_PROMOTED_TEXT")
                  }}</span>
                </div>
              </div>
            </div>

            <div class="recipe-table-content">
              <div
                class="add-zero-section"
                v-if="
                  (!categoryPromotedRecipes ||
                    categoryPromotedRecipes.length === 0) &&
                  !isPageLoading &&
                  !isUpdating
                "
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray">
                    0 {{ $t("CATEGORY.PROMOTED_RECIPES") }}.
                  </span>
                  <span class="text-title-2 font-normal color-spanish-gray">
                    {{ $t("CATEGORY.RECIPE_AUTO_SELECTED") }}
                  </span>
                </div>
              </div>

              <simple-table
                v-if="categoryPromotedRecipes.length > 0"
                :column-names="promotedRecipeColumnNames"
                :column-keys="promotedRecipeColumnKeys"
                :data-source="categoryPromotedRecipes"
                table-class="content-recipe-table"
              >
                <template v-slot:image="props">
                  <img
                    :src="getRecipeImageUrl(props.data)"
                    :alt="props.data?.title"
                    class="category-recipe-image"
                    @error="$event.target.src = defaultImage"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="text-light-h4 color-stone-gray">{{
                    props.data?.isin
                  }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="text-h3 font-bold">{{
                    getRecipeTitleText(props.data)
                  }}</span>
                  <languages-alert
                    :languages="props.data?.langs"
                    :has-alert="props.data?.hasAlert"
                    :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
                  ></languages-alert>
                </template>

                <template v-slot:totalTime="props">
                  <span class="text-light-h3">{{
                    getRecipeTimeText(props.data)
                  }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="text-light-h3"
                    >{{ getIngredientCountText(props.data) }} ingredients</span
                  >
                </template>

                <template v-slot:actions="props">
                  <div class="category-recipe-actions">
                    <body-menu
                      :actions="getPromotedRecipeActionsData(props.data)"
                      @call-actions="$emit('handle-recipe-action', $event)"
                    />
                  </div>
                </template>
              </simple-table>
            </div>
          </div>
        </div>
      </div>
      <div class="recipes-table-content">
        <div class="content">
          <div class="recipe-category">
            <div class="recipe-header-section">
              <div class="category-recipe-header-text">
                <span class="font-size-24 text-h2 font-normal">
                  {{ recipeForCategoriesTotal }}
                  {{
                    recipeForCategoriesTotal === 1
                      ? $t("CATEGORY.RECIPE_IN_CATEGORY")
                      : $t("CATEGORY.RECIPES_IN_CATEGORY")
                  }}
                </span>
              </div>
              <div
                class="category-recipe-header-actions"
                v-if="!isSelectionEnabled"
              >
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="$emit('select-recipes')"
                  v-if="recipeDataForCategories.length > 0"
                >
                  {{ $t("COMMON.SELECT") }}
                </button>
                <search-bar
                  :isInForm="true"
                  customPlaceholder="Search for recipe name"
                  searchContext="details-page"
                />
                <button
                  type="button"
                  class="btn-green-text btn-small"
                  @click="$emit('add-recipe-to-category')"
                >
                  <img
                    alt="add"
                    src="@/assets/images/category-add.png?skipsvgo=true"
                  />
                  <span>{{ $t("BUTTONS.ADD_RECIPE") }}</span>
                </button>
              </div>
            </div>

            <RecipeSelectionPanel
              v-if="isSelectionEnabled"
              :selection-of-recipes="selectionOfRecipes"
              :check-selected-recipes="checkSelectedRecipes"
              @select-all-matches="$emit('select-all-matches')"
              @remove-all-selected="$emit('remove-all-selected')"
              @delete-select="$emit('delete-select')"
              @cancel-select="$emit('cancel-select')"
            />
            <div class="recipe-table-content">
              <div
                class="no-result-for-category text-title-2"
                v-if="recipeDataForCategories.length === 0 && !isPageLoading && hasSearchQuery"
              >
                {{ $t("COMMON.NO_RESULT_FOUND") }}
              </div>
              <div
                class="add-zero-section content-recipe-section"
                v-if="recipeDataForCategories.length === 0 && !isPageLoading && !hasSearchQuery"
              >
                <div class="zero-promoted">
                  <span class="text-title-2 color-spanish-gray"
                    >0 {{ $t("CATEGORY.RECIPES_IN_CATEGORY") }}.</span
                  >
                  <span class="text-title-2 font-normal color-spanish-gray">{{
                    $t("CATEGORY.ADD_RECIPE_TO_CATEGORY")
                  }}</span>
                </div>
              </div>

              <simple-table
                v-if="recipeDataForCategories.length > 0"
                :column-names="categoryRecipeColumnNames"
                :column-keys="categoryRecipeColumnKeys"
                :data-source="recipeDataForCategories"
                table-class="content-recipe-table"
                :data-selected-highlight="(data) => isSelectionEnabled && data.isSelectedToDelete ? 'highlight' : 'none'"
                @row-click="$emit('table-row-click-action', $event)"
                :body-style="{
                  cursor: isSelectionEnabled ? 'pointer' : 'default',
                }"
              >
                <template v-slot:checkbox="props" v-if="isSelectionEnabled">
                  <div class="edit-product-table-checkbox">
                    <div class="edit-select-all-checkbox-section">
                      <label class="checkbox checkbox-20 checkbox-without-text" :for="'checkbox-' + props.index" @click="(event) => $emit('handle-checkbox-click', props.index, props.data, event)">
                        <input
                          type="checkbox"
                          :id="'checkbox-' + props.index"
                          :checked="props.data.isSelectedToDelete || false"
                          :aria-label="'Select item ' + (props.index + 1)"
                        />
                        <span class="checkmark"></span>
                      </label>
                    </div>
                  </div>
                </template>

                <template v-slot:image="props">
                  <img
                    :src="getRecipeImageUrl(props.data)"
                    :alt="props.data?.title"
                    class="category-recipe-image"
                    @error="$event.target.src = defaultImage"
                  />
                </template>

                <template v-slot:isin="props">
                  <span class="text-light-h4 color-stone-gray">{{
                    props.data?.isin
                  }}</span>
                </template>

                <template v-slot:title="props">
                  <span class="text-h3 font-bold">{{
                    getRecipeTitleText(props.data)
                  }}</span>
                  <languages-alert
                    :languages="props.data?.langs"
                    :has-alert="props.data?.hasAlert"
                    :alert-tooltip-text="$t('COMMON.RECIPE_LANG_ALERT')"
                  ></languages-alert>
                </template>

                <template v-slot:totalTime="props">
                  <span class="text-light-h3">{{
                    getRecipeTimeText(props.data)
                  }}</span>
                </template>

                <template v-slot:ingredientCount="props">
                  <span class="text-light-h3"
                    >{{ getIngredientCountText(props.data) }} ingredients</span
                  >
                </template>

                <template v-slot:actions="props">
                  <div
                    class="category-recipe-actions"
                    v-if="!isSelectionEnabled"
                  >
                    <div
                      :class="{
                        'simple-data-tooltip': props.data.status !== 'active',
                      }"
                      :data-tooltip-text="props.data.status !== 'active' && $t('COMMON.CANNOT_PROMOTE_UNPUBLISHED_RECIPES')"
                    >

                      <button
                        type="button"
                        class="btn-green-outline"
                        :disabled="props.data.status !== 'active'"
                        @click="$emit('promote-recipe', props.data)"
                      >
                        {{ $t('COMMON.PROMOTE') }}
                      </button>
                    </div>
                    <body-menu
                      :actions="getCategoryRecipeActionsData(props.data)"
                      @call-actions="$emit('handle-recipe-action', $event)"
                    />
                  </div>
                </template>
              </simple-table>

              <pagination
                v-if="
                  recipeForCategoriesTotal > sizeRecipe &&
                  recipeDataForCategories.length > 0
                "
                :list="recipeDataForCategories"
                :list-total="recipeForCategoriesTotal"
                :size-per-page="sizeRecipe"
                :current-page="Math.floor(fromRecipe / sizeRecipe) + 1"
                @page-change="$emit('handle-recipes-page-change', $event)"
              />
            </div>
          </div>
        </div>
      </div>
    </simple-content-wrapper>
  </div>
</template>

<script setup>
import { useNuxtApp } from "#app";
import SimpleContentWrapper from "@/components/simple-content-wrapper.vue";
import SimpleTable from "@/components/simple-table/simple-table.vue";
import SearchBar from "@/components/search-bar/search-bar.vue";
import Pagination from "@/components/pagination.vue";
import BodyMenu from "@/components/body-menu.vue";
import LanguagesAlert from "@/components/languages-alert.vue";
import RecipeSelectionPanel from "./RecipeSelectionPanel.vue";
import defaultImage from "~/assets/images/default_recipe_image.png";

const { $t } = useNuxtApp();

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
  recipeDataForCategories: {
    type: Array,
    default: () => [],
  },
  categoryPromotedRecipes: {
    type: Array,
    default: () => [],
  },
  categoryPromotedRecipesTotal: {
    type: Number,
    default: 0,
  },
  recipeForCategoriesTotal: {
    type: Number,
    default: 0,
  },
  isPageLoading: {
    type: Boolean,
    default: false,
  },
  isUpdating: {
    type: Boolean,
    default: false,
  },
  isSelectionEnabled: {
    type: Boolean,
    default: false,
  },
  sizeRecipe: {
    type: Number,
    default: 10,
  },
  fromRecipe: {
    type: Number,
    default: 0,
  },
  promotedRecipeColumnNames: {
    type: Array,
    default: () => [],
  },
  promotedRecipeColumnKeys: {
    type: Array,
    default: () => [],
  },
  categoryRecipeColumnNames: {
    type: Array,
    default: () => [],
  },
  categoryRecipeColumnKeys: {
    type: Array,
    default: () => [],
  },
  selectionOfRecipes: {
    type: Array,
    default: () => [],
  },
  checkSelectedRecipes: {
    type: Number,
    default: 0,
  },
  getRecipeImage: {
    type: Function,
    required: true,
  },
  getRecipeTitle: {
    type: Function,
    required: true,
  },
  getRecipeTime: {
    type: Function,
    required: true,
  },
  getIngredientCount: {
    type: Function,
    required: true,
  },
  getPromotedRecipeActions: {
    type: Function,
    required: true,
  },
  getCategoryRecipeActions: {
    type: Function,
    required: true,
  },
  hasSearchQuery: {
    type: Boolean,
    default: false,
  },
});

defineEmits([
  'add-recipe-to-category',
  'select-recipes',
  'handle-recipe-action',
  'table-row-click-action',
  'handle-checkbox-click',
  'promote-recipe',
  'handle-recipes-page-change',
  'select-all-matches',
  'remove-all-selected',
  'delete-select',
  'cancel-select'
]);

const getRecipeImageUrl = (recipe) => {
  return props.getRecipeImage(recipe);
};

const getRecipeTitleText = (recipe) => {
  return props.getRecipeTitle(recipe);
};

const getRecipeTimeText = (recipe) => {
  return props.getRecipeTime(recipe);
};

const getIngredientCountText = (recipe) => {
  return props.getIngredientCount(recipe);
};

const getPromotedRecipeActionsData = (recipe) => {
  return props.getPromotedRecipeActions(recipe);
};

const getCategoryRecipeActionsData = (recipe) => {
  return props.getCategoryRecipeActions(recipe);
};
</script>
