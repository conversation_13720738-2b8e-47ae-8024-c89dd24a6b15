<template>
  <content-wrapper
    wrapper-classes="content-item font-family-averta"
    :is-body-loading="props.isEdit ? isLoading : false"
  >
    <page-top-block
      :hint="hintID"
      :page-actions-back-label="$t('CATEGORY.BACK_MESSAGE')"
      :page-actions-back-path="state.backToCategoryPath.value"
      :background-image="image"
      :page-actions-is-continue-disabled="!isContinueButtonEnabled"
      :page-actions-continue-label="continueButtonLabel"
      @page-actions-continue="openPreviewSaveModal"
      @page-actions-cancel="() => navigateTo(state.backToCategoryPath.value)"
    >
      <CategoryFormHeader
        v-model:name="categoriesName"
        v-model:slug="categoriesSlug"
        v-model:image="image"
        v-model:status="categoriesState"
        :is-edit="props.isEdit"
        :categories-state="categoriesState"
        :has-slug-exist="hasSlugExist"
        :is-category-include-in-hero="isCategoryIncludeInHero"
        :recipe-data-for-categories="recipeDataForCategories"
        :category-promoted-recipes="categoryPromotedRecipes"
        :image-loaded-size="imageLoadedSize"
        :image-upload-percentage="imageUploadPercentage"
        :icon-info-outline-default-style="iconInfoOutlineDefaultStyle"
        @slug-input="handleSlugInput"
        @publish-toggle="publishToggleBtnAsync"
        @publish-toggle-popup="publishToggleBtnPopup"
        @publish-toggle-confirmation="publishToggleConfirmation"
        @delete-category="deleteCategory"
        @file-upload="handleFileUpload"
      />

      <CategoryVariantsSection
        v-if="finalAvailableLangs && finalAvailableLangs.length > 1"
        v-model:variants="recipeVariantList"
        :lang="lang"
        :is-category-alert-icon="isCategoryAlertIcon"
        :recipe-variant-language-list="recipeVariantLanguageList"
        :icon-info-outline-default-style="iconInfoOutlineDefaultStyle"
        :category-associations="categoryAssociations"
        @open-variant-popup="openRecipeVariantPopUp"
        @input-content-changed="inputContentChanged"
        @delete-variant="deleteCategoryVariant"
      />
    </page-top-block>
    <CategoryRecipesSection
      :is-edit="props.isEdit"
      :recipe-data-for-categories="recipeDataForCategories"
      :category-promoted-recipes="categoryPromotedRecipes"
      :category-promoted-recipes-total="categoryPromotedRecipesTotal"
      :recipe-for-categories-total="recipeForCategoriesTotal"
      :is-page-loading="isPageLoading"
      :is-updating="isUpdating"
      :is-selection-enabled="isSelectionEnabled"
      :size-recipe="sizeRecipe"
      :from-recipe="fromRecipe"
      :promoted-recipe-column-names="promotedRecipeColumnNames"
      :promoted-recipe-column-keys="promotedRecipeColumnKeys"
      :category-recipe-column-names="categoryRecipeColumnNames"
      :category-recipe-column-keys="categoryRecipeColumnKeys"
      :selection-of-recipes="selectionOfRecipes"
      :check-selected-recipes="checkSelectedRecipes"
      :get-recipe-image="getRecipeImage"
      :get-recipe-title="getRecipeTitle"
      :get-recipe-time="getRecipeTime"
      :get-ingredient-count="getIngredientCount"
      :get-promoted-recipe-actions="getPromotedRecipeActions"
      :get-category-recipe-actions="getCategoryRecipeActions"
      :has-search-query="hasSearchQuery"
      @add-recipe-to-category="addRecipeToCategory"
      @select-recipes="selectRecipes"
      @handle-recipe-action="handleRecipeAction"
      @table-row-click-action="tableRowClickAction"
      @handle-checkbox-click="handleCheckboxClick"
      @promote-recipe="promoteRecipe"
      @handle-recipes-page-change="handleRecipesPageChange"
      @select-all-matches="selectAllMatches"
      @remove-all-selected="removeAllSelected"
      @delete-select="deleteSelect"
      @cancel-select="cancelSelect"
    />

    <CategoryFormModals
      :has-recipe-variant-language-popup="hasRecipeVariantLanguagePopUp"
      :is-add-variant-category-name-popup="isAddVariantCategoryNamePopUp"
      :recipe-variant-language-list="recipeVariantLanguageList"
      :has-recipe-variant-language-result="hasRecipeVariantLanguageResult"
      :recipe-variant-selected-language="recipeVariantSelectedLanguage"
      :categories-name="categoriesName"
      @close-variant-language-popup="() => (hasRecipeVariantLanguagePopUp = false)"
      @close-add-variant-popup="() => (isAddVariantCategoryNamePopUp = false)"
      @next-variant-popup="nextCategoryVariantNameModalPopUp"
      @set-recipe-variant-language-matches="setRecipeVariantLanguageMatches"
      @show-recipe-variant-language-matches="showRecipeVariantLanguageMatches"
      @add-confirm-variant="addRecipeVariant"
      @back-to-route="backToSelectLanguageVariantPopUp"
    />
  </content-wrapper>
</template>

<script setup>
import { onMounted, onUnmounted } from "vue";
import { onBeforeRouteLeave } from "vue-router";
import { useNuxtApp } from "#app";
import ContentWrapper from "@/components/content-wrapper/content-wrapper.vue";
import PageTopBlock from "@/components/page-top-block.vue";
import CategoryFormHeader from "./CategoryFormHeader.vue";
import CategoryVariantsSection from "./CategoryVariantsSection.vue";
import CategoryRecipesSection from "./CategoryRecipesSection.vue";
import CategoryFormModals from "./CategoryFormModals.vue";
import SaveModal from "@/components/save-modal.vue";
import CancelModal from "@/components/cancel-modal.vue";
import SavingModal from "@/components/saving-modal.vue";
import DeleteModal from "@/components/delete-modal.vue";
import AddRecipeModal from "@/components/add-recipe-modal.vue";
import RecipePreviewDetailModal from "@/components/pages/recipes/recipe-preview-detail-modal.vue";
import { PROCESS_MODAL_TYPE } from '@/models/process-modal.model';
import ProcessModal from '@/components/modals/process-modal.vue';
import { useBaseModal } from "@/composables/useBaseModal.js";
import ConfirmModal from "../../modals/confirm-modal.vue";
import { CONFIRM_MODAL_TYPE } from "../../../models/confirm-modal.model.js";

import { useCategoryFormLogic } from "@/composables/category/useCategoryFormLogic.js";
import { useCategoryFormMethods } from "@/composables/category/useCategoryFormMethods.js";
import { useCategorySaveOperations } from "@/composables/category/useCategorySaveOperations.js";
import { useCategoryRecipeOperations } from "@/composables/category/useCategoryRecipeOperations.js";
import { useCategorySelectionOperations } from "@/composables/category/useCategorySelectionOperations.js";
import { useCategoryDataOperations } from "@/composables/category/useCategoryDataOperations.js";
import { useCategoryModalOperations } from "@/composables/category/useCategoryModalOperations.js";
import { useCommonUtils } from "@/composables/useCommonUtils.js";
import { useSearchStore } from "@/stores/search.js";

const props = defineProps({
  isEdit: {
    type: Boolean,
    default: false,
  },
});

const { $t } = useNuxtApp();
const { scrollToTop, triggerLoading } = useCommonUtils();

// Use the composables
const state = useCategoryFormLogic(props);
const methods = useCategoryFormMethods(state, {
  userDataStore: state.userDataStore,
  categoryStore: state.categoryStore,
  $t: state.$t,
  $keys: state.$keys,
  parseDurationString: state.parseDurationString,
  getSearchQuery: state.getSearchQuery,
});

// Initialize operation composables
const saveOperations = useCategorySaveOperations(state, methods);
const recipeOperations = useCategoryRecipeOperations(state, { scrollToTop, triggerLoading });
const selectionOperations = useCategorySelectionOperations(state, recipeOperations);
const dataOperations = useCategoryDataOperations(state);
const modalOperations = useCategoryModalOperations(state, saveOperations, selectionOperations, dataOperations, recipeOperations);
const searchStore = useSearchStore();

const { openModal, closeModal } = useBaseModal({
  CategorySaveModal: SaveModal,
  CategoryCancelModal: CancelModal,
  CategorySavingModal: SavingModal,
  CategoryDeleteModal: DeleteModal,
  CategorySelectDeleteModal: DeleteModal,
  CategoryAddRecipeModal: AddRecipeModal,
  CategoryRecipePreviewModal: {
    component: RecipePreviewDetailModal,
    hideCloseBtn: false,
    modalWrapperClass: "recipe-preview-detail-modal-wrapper",
    skipClickOutside: true,
  },
  deletingModal: {
    component: ProcessModal,
    skipClickOutside: true,
    skipEscapeClick: true,
    props: {
      modalType: PROCESS_MODAL_TYPE.DELETING,
    },
  },
  confirmModal: ConfirmModal
});

const {
  isLoading,
  categoriesName,
  categoriesSlug,
  image,
  hasSlugExist,
  hasChanges,
  recipeVariantSelectedLanguage,
  categoriesState,
  isCategoryIncludeInHero,
  recipeDataForCategories,
  categoryPromotedRecipes,
  recipeForCategoriesTotal,
  categoryPromotedRecipesTotal,
  fromRecipe,
  sizeRecipe,
  isPageLoading,
  isUpdating,
  lang,
  isSelectionEnabled,
  selectionOfRecipes,
  recipeVariantList,
  recipeVariantLanguageList,
  hasRecipeVariantLanguagePopUp,
  hasRecipeVariantLanguageResult,
  isAddVariantCategoryNamePopUp,
  categoryAssociations,
  finalAvailableLangs,
  isCategoryAlertIcon,
  iconInfoOutlineDefaultStyle,
  promotedRecipeColumnNames,
  promotedRecipeColumnKeys,
  categoryRecipeColumnNames,
  categoryRecipeColumnKeys,
  hintID,
  isContinueButtonEnabled,
  checkSelectedRecipes,
  imageUploadPercentage,
  imageLoadedSize,
  continueButtonLabel,
  hasSearchQuery,
} = state;

const { handleSlugInput, getRecipeImage, getRecipeTime, getRecipeTitle, getIngredientCount, getPromotedRecipeActions, getCategoryRecipeActions, inputContentChanged, cleanup } = methods;
const publishToggleBtnAsync = () => modalOperations.publishToggleBtnAsync();
const publishToggleBtnPopup = () => modalOperations.publishToggleBtnPopup(openModal);
const publishToggleConfirmation = () => modalOperations.publishToggleConfirmation(openModal, closeModal);
const deleteCategory = () => modalOperations.deleteCategory(openModal, closeModal);
const openPreviewSaveModal = () => modalOperations.openPreviewSaveModal(openModal, closeModal);
const addRecipeToCategory = () => modalOperations.addRecipeToCategory(openModal, closeModal);
const handleRecipeAction = async (action) => await modalOperations.handleRecipeAction(action, openModal, closeModal);
const deleteSelect = () => modalOperations.deleteSelect(openModal, closeModal);
const deleteCategoryVariant = (variant, index) => modalOperations.deleteCategoryVariant(variant, index, openModal, closeModal);
const promoteRecipe = async (recipe) => await recipeOperations.promoteRecipe(recipe);
const handleRecipesPageChange = (page) => recipeOperations.handleRecipesPageChange(page);
const selectRecipes = () => selectionOperations.selectRecipes();
const selectAllMatches = () => selectionOperations.selectAllMatches();
const tableRowClickAction = (data, index) => selectionOperations.tableRowClickAction(data, index);
const handleCheckboxClick = (index, data, event) => selectionOperations.handleCheckboxClick(index, data, event);
const cancelSelect = () => selectionOperations.cancelSelect();
const removeAllSelected = () => selectionOperations.removeAllSelected();
const openRecipeVariantPopUp = () => dataOperations.openRecipeVariantPopUp();
const setRecipeVariantLanguageMatches = (value, index) => dataOperations.setRecipeVariantLanguageMatches(value, index);
const showRecipeVariantLanguageMatches = () => dataOperations.showRecipeVariantLanguageMatches();
const nextCategoryVariantNameModalPopUp = (item) => dataOperations.nextCategoryVariantNameModalPopUp(item);
const backToSelectLanguageVariantPopUp = () => dataOperations.backToSelectLanguageVariantPopUp();
const addRecipeVariant = (item) => dataOperations.addRecipeVariant(item);

// Lifecycle hooks
onMounted(async () => {
  try {
    await dataOperations.initializeLanguageVariants();

    searchStore.setSearchQuery("", { emitQueryParam: false, context: 'details-page' })
    if (props.isEdit && state.route.params.isin) {
      await Promise.all([
        dataOperations.getEditCategoryListAsync(state.route.params.isin, state.lang.value),
        recipeOperations.getPromotedRecipesForCategoriesAsync(state.route.params.isin, state.lang.value),
        dataOperations.getSearchConfigAsync(state.route.params.isin),
      ]);
      await recipeOperations.getRecipeDataForCategoriesAsync(state.route.params.isin, state.lang.value);
    } else {
      await Promise.all([
        methods.getCategoryISINAsync(),
        dataOperations.getSearchConfigAsync(state.route.params.isin),
      ]);
    }

    state.isInitializing.value = false;
  } catch (error) {
    console.error("[IQ][CategoryForm] Error during component initialization:", error);
  } finally {
    state.isLoading.value = false;
  }
});

onBeforeRouteLeave((_to, _from, next) => {
  if (hasChanges.value) {
    openModal({
      name: "CategoryCancelModal",
      props: {
        closeModal: () => {
          closeModal("CategoryCancelModal");
          next(false);
        },
        callConfirm: () => {
          closeModal("CategoryCancelModal");
          next();
        },
      },
    });
  } else {
    next();
  }
});

onUnmounted(() => {
  cleanup();
});

const handleFileUpload = (file) => {
  const result = state.uploadFile(file);

  if (result.shouldShowWarning) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.CONTINUE_LOAD,
        title: $t('DESCRIPTION_POPUP.LARGER_FILE'),
        descriptionRed: $t('DESCRIPTION_POPUP.OPTIMAL_IMAGE'),
      },
      onClose: (response) => response && result.upload(),
    });
  } else if (result.shouldShowError) {
    openModal({
      name: "confirmModal",
      props: {
        modalType: CONFIRM_MODAL_TYPE.UNABLE,
        title: $t('DESCRIPTION_POPUP.MAX_IMAGE_SIZE'),
        description: $t('DESCRIPTION_POPUP.MAX_IMAGE'),
        hideCancelBtn: true
      },
    });
  } else if (result.shouldUpload) {
    result.upload();
  }
};
</script>
