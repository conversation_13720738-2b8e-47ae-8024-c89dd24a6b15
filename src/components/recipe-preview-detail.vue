<template>
  <div class="recipe-preview-page">
    <div class="outer-section">
      <recipe-preview-variant-section
        v-if="isVariantExist"
        :recipePreviewVariantLanguageList="recipePreviewVariantLanguageList"
        :recipePreviewVariantSelectedLanguage="recipePreviewVariantSelectedLanguage"
        :isVariantDropdownVisible="isVariantDropdownVisible"
        @openVariantDropdown="openVariantDropdown"
        @chooseRecipeVariant="chooseRecipeVariant"
      ></recipe-preview-variant-section>
      <div v-if="isDataLoading" class="recipe-preview-loading-main">
        <div class="content">
          <div class="input-loading">
            <div class="loader-image"></div>
          </div>
          <div class="loading-text">
            <p>{{ $t("LOADER.LOADING") }}</p>
          </div>
        </div>
      </div>
      <div v-show="!isDataLoading" class="main-section">
        <div class="recipe-details-video-image-container">
          <div class="recipe-basic-details">
            <div class="recipe-video-image">
              <recipe-preview-media-section
                :isImageVisible="isImageVisible"
                :productImage="productImage"
                :imageUrlLinkUpdate="imageUrlLinkUpdate"
                :isVideoVisible="isVideoVisible"
                :productVideo="productVideo"
                :isVideoLoaded="isVideoLoaded"
                :isLinkPresent="isLinkPresent"
                :linkURLImage="linkURLImage"
                :imageUrlDomain="imageUrlDomain"
                :recipeName="recipeName"
                :defaultImage="defaultImage"
                :placeholderImage="placeholderImage"
                :showImageVideo="showImageVideo"
                :openImageLinkPage="openImageLinkPage"
                :videoPopup="videoPopup"
                :openLinkPage="openLinkPage"
                :checkVideoLoading="checkVideoLoading"
              >
              </recipe-preview-media-section>
              <recipe-preview-head-section
                :recipeName="recipeName"
                :recipeSubtitle="recipeSubtitle"
                :totalTime="totalTime"
                :preprationTime="preparationTime"
                :cookingTime="cookingTime"
                :description="description"
                :recipePrice="Number(recipePrice)"
                :recipeCurrency="recipeCurrency"
                :notes="notes"
              >
              </recipe-preview-head-section>
            </div>
          </div>
        </div>
        <div class="recipe-steps-at-recipe-preview">
          <div class="recipe-steps-notes-container">
            <recipe-preview-steps-section
              :tasksDataPreview="duplicateTask"
              :isStepIngredientsDataLoading="isStepIngredientsDataLoading"
              :recipePreviewVariantDefaultLanguage="lang"
              @openRecipeStepVideoPopup="openRecipeStepVideoPopup"
            ></recipe-preview-steps-section>
            <div class="notes-section">
              <div class="notes-heading">{{ $t("RECIPE_PREVIEW.NOTES") }}:</div>
              <div v-if="notes" class="notes-available">
                {{ notes }}
              </div>
              <div v-else class="no-notes-available">
                {{ $t("RECIPE_PREVIEW.NO_NOTES_TO_SHOW") }}
              </div>
            </div>
            <recipe-preview-nutrition-section
              :nutrientTableData="nutrientTableData"
              :nutritionServingSize="nutritionServingSize"
              :nutritionServingSizePerContainer="
                nutritionServingSizePerContainer
              "
              :isNutritionDropDownIconVisible="isNutritionDropDownIconVisible"
              :isNutritionDropdownResultVisible="
                isNutritionDropdownResultVisible
              "
              :selectedNutritionType="selectedNutritionType"
              :nutritionServingList="nutritionServingList"
              :choosePerServingPer100g="choosePerServingPer100g"
              @selectedNutrition="selectedNutrition"
              @showNutritionDropDown="showNutritionDropDown"
            />
          </div>
          <div class="ingredients-categories">
            <recipe-preview-ingredient-section
              :servings="Number(servings)"
              :availableServings="availableServings"
              :isIngredientsDataLoading="isIngredientsDataLoading"
              :isStepIngredientsDataLoading="isStepIngredientsDataLoading"
              :selectedServingsName="String(selectedServingsName)"
              :ingredientsDataPreview="duplicateIngredient"
              :isServingsDropdownResultVisible="isServingsDropdownResultVisible"
              :isMasterPreview="true"
              @showServingsData="showServingsData"
              @selectedServingsProductAsync="selectedServingsProductAsync"
            />
            <recipe-preview-filters-section
              :selectedDietsPreview="selectedDiets"
              :selectedCategoriesPreview="selectedCategories"
              :selectedTagsPreview="selectedTags"
              :allergensList="allergensList"
              :selectedAllergens="selectedAllergens"
              :lang="lang"
            />
            <recipe-preview-slug-section
              :slugEdit="slugEdit"
              :recipeAttributionAuthor="recipeAttributionAuthor"
              :publisherImagePreview="selectedPublisherImage"
              :selectedPublisherName="selectedPublisherName"
              :isShowPublisher="isShowPublisher"
              :recipeAttributionExternalId="recipeAttributionExternalId"
            />
          </div>
        </div>
      </div>
    </div>
    <recipeVideo
      v-if="isVideoModalVisible"
      :videoLink="videoLink"
      :closeModal="closeModal"
    />
  </div>
</template>

<script setup>
import {
  ref,
  onMounted,
  onBeforeUnmount,
  getCurrentInstance,
} from "vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import recipeVideo from "@/components/recipeVideo";
import FoodItemService from "@/services/FoodItemService";
import recipePreviewVariantSection from "./recipe-preview/recipe-preview-variant-section.vue";
import recipePreviewStepsSection from "./recipe-preview/recipe-preview-steps-section.vue";
import recipePreviewMediaSection from "./recipe-preview/recipe-preview-media-section.vue";
import recipePreviewHeadSection from "./recipe-preview/recipe-preview-head-section.vue";
import recipePreviewIngredientSection from "./recipe-preview/recipe-preview-ingredient-section.vue";
import recipePreviewFiltersSection from "./recipe-preview/recipe-preview-filters-section.vue";
import recipePreviewSlugSection from "./recipe-preview/recipe-preview-slug-section.vue";
import recipePreviewNutritionSection from "./recipe-preview/recipe-preview-nutrition-section.vue";

// images
import placeholderImage from "~/assets/images/Eclipse-1s-200px.gif";
import defaultImage from "~/assets/images/recipe-detail-upload.png";
import emptyVideoImage from "~/assets/images/empty-video.png";
import defaultOrganizationsImage from "@/assets/images/default_organizations.png";

// composables
import { useRefUtils } from '@/composables/useRefUtils';
import { useProjectLang } from "@/composables/useProjectLang";
import { useCommonUtils } from "@/composables/useCommonUtils";


import { useI18n } from "vue-i18n";


const { extractTimeParts } = useCommonUtils();

const props = defineProps({
  rISIN: String,
  checkRecipePreviewVideo: Function,
});
const emit = defineEmits(["loaderProgress"]);

const store = useStore();
const router = useRouter();
const { t } = useI18n();
const instance = getCurrentInstance();
const $keys = instance.appContext.config.globalProperties.$keys;
const { $tracker } = useNuxtApp();
const { readyProject } = useProjectLang();
const { getRef } = useRefUtils();

// Reactive state variables
const project = reactive({});
const isVideoLoaded = ref(false);
const videoLink = ref("");
const recipeCurrency = ref("");
const recipePrice = ref("");
const imageUrlLinkUpdate = ref("");
const isDataLoading = ref(false);
const publisherDataList = ref([]);
const recipeAttributionOrganization = ref("");
const selectedPublisherName = ref("");
const selectedPublisherImage = ref("");
const recipeAttributionAuthor = ref("");
const recipeAttributionExternalId = ref("");
const linkURLImage = ref("");
const isVideoModalVisible = ref(false);
const isVideoMuted = ref(false);
const recipeName = ref("");
const recipeSubtitle = ref("");
const slugEdit = ref("");
const ingredientsData = ref({});
const diets = ref([]);
const dietsList = ref([]);
const allergensList = ref([]);
const categoriesList = ref([]);
const selectedCategories = ref([]);
const selectedDiets = ref([]);
const selectedAllergens = ref([]);
const selectedTags = ref([]);
const nutrientUnitsConstants = ref([]);
const productImage = ref("");
const productVideo = ref("");
const description = ref("");
const servings = ref("");
const availableServings = ref([]);
const isImageVisible = ref(false);
const imageUrlDomain = ref("");
const isVideoVisible = ref(false);
const totalTime = ref("");
const cookingTime = ref("");
const preparationTime = ref("");
const hour = ref("");
const minute = ref("");
const second = ref("");
const prepHour = ref("");
const prepMinute = ref("");
const cookHour = ref("");
const cookMinute = ref("");
const cookSecond = ref("");
const title = ref(true);
const ingredients = ref([]);
const recipeIsin = ref("");
const recipeData = ref("");
const ingredientsIsinList = ref([]);
const notes = ref("");
const editRecipeQuery = ref("");
const yieldData = ref("");
const hostNameUrlLink = ref("");
const duplicateIngredient = reactive({});
const duplicateTask = ref([]);
const isImageModified = ref(false);
const isVideoModified = ref(false);
const editInstructionClicked = ref([]);
const tasksData = ref([]);
const isIngredientsDataLoading = ref(false);
const isStepIngredientsDataLoading = ref(false);
const isVideoPresent = ref(false);
const videoDimension = ref("");
const urlLinkUpdate = ref("");
const hostNameUrlLinkUpdate = ref("");
const isLinkPresent = ref(false);
const recipeStepVideo = ref("");
const isRecipeStepVideoOpen = ref(false);
const isStepVideoMuted = ref(false);
const isServingsDropdownResultVisible = ref(false);
const selectedServingsName = ref("");
const isVariantDropdownVisible = ref(false);
const isVariantExist = ref(false);
const varaiantdropdowndata = ref([]);
const recipePreviewVariantLanguageList = ref([]);
const lang = ref("");
const defaultLang = ref("");
const recipePreviewVariantSelectedLanguage = ref("");
const nutritionDataList = ref([]);
const displayNutritionalDataList = ref([]);
const isNutritionDropDownIconVisible = ref(false);
const isNutritionDropdownResultVisible = ref(false);
const selectedNutritionType = ref("");
const nutritionServingList = ref([
  { name: "per Serving", type: "perServing" },
  { name: "per 100g", type: "per100g" },
]);
const nutritionServingSize = ref("");
const nutritionServingSizePerContainer = ref("");
const nutrientTableData = ref({
  perServing: [],
  per100g: [],
});
const addRecipeNutrientType = ref("");
const nutritionKey = ref({});
const isShowPublisher = ref(false);

onMounted(async () => {
  readyProject(async ({ isProjectReady }) => {
    if (isProjectReady) {
      isDataLoading.value = true;
      recipeIsin.value = props.rISIN || "";
      Object.assign(project, store.getters["userData/getProject"]);
      lang.value = defaultLang.value = store.getters["userData/getDefaultLang"];
      recipePreviewVariantSelectedLanguage.value = defaultLang.value;

      await loadRecipeDataAsync();
      if (recipeIsin.value) {
        setupDuplicateIngredient();
        await getRecipeAsync(recipeIsin.value);
        isImageVisible.value = true;
      }
      await getOrganizationsListAsync();
      addDocumentEventListeners();
    }
  });
});

onBeforeUnmount(() => {
  document.removeEventListener("input", handleClickOutside);
  document.removeEventListener("keyup", handleESCClickOutside);
});
const loadRecipeDataAsync = async () => {
  await getRecipeUnitConfigAsync();
  await getRecipeDietsAsync();
  await getRecipeAllergensAsync();
  await getFeatureConfigAsync();
  await getNutritionalDataAsync();
};

const setupDuplicateIngredient = () => {
  duplicateIngredient.children = [];
};

const addDocumentEventListeners = () => {
  document.addEventListener("click", handleClickOutside);
  document.addEventListener("keyup", handleESCClickOutside);
};

const checkVideoLoading = () => {
  isVideoLoaded.value = true;
};

const handleESCClickOutside = (event) => {
  if (event?.key === "Escape") {
    closeModal();
  }
};

const choosePerServingPer100g = () => {
  if (selectedNutritionType.value === "perServing") {
    return nutrientTableData.value['perServing'];
  } else if (selectedNutritionType.value === "per100g") {
    return nutrientTableData.value['per100g'];
  }
  if (
    !nutritionKey?.value?.['per100g'] &&
    !nutritionKey?.value?.['perServing']
  ) {
    selectedNutritionType.value = addRecipeNutrientType.value;
  }
};

const getNutritionalDataAsync = async () => {
  try {
    await store.dispatch("recipeDetails/getNutritionalDataAsync");
    const response = store.getters["recipeDetails/getNutritionalData"];
    if (response?.nutrients) {
      nutritionDataList.value = response.nutrients;
      if (nutritionKey.value?.per100g || nutritionKey.value?.perServing) {
        nutritionDataList.value = response.nutrients;
      } else {
        addRecipeNutrientType.value = response?.type ?? "";
        nutritionDataList.value = response.nutrients;
        let displayNutritionalMainDataList = [];
        compareNutritionData(displayNutritionalMainDataList);
        nutrientTableData.value[response.type] = displayNutritionalDataList.value;
      }
    }
  } catch (error) {
    console.error(`${$keys.KEY_NAMES.ERROR_IN} getNutritionalData`, error);
  }
};

const selectedNutrition = (type) => {
  selectedNutritionType.value = type;
  isNutritionDropdownResultVisible.value = false;
};

const showNutritionDropDown = () => {
  isNutritionDropdownResultVisible.value =
    !isNutritionDropdownResultVisible.value;
};

const getRecipeUnitConfigAsync = async () => {
  const params = { lang: lang.value };
  try {
    await store.dispatch("ingredient/getRecipeUnitConfigAsync", { params });
    const response = store.getters["ingredient/getRecipeUnit"];
    if (response?.units?.length) {
      nutrientUnitsConstants.value = response.units;
    }
  } catch (error) {
    console.error("Error in getRecipeUnitConfigAsync", error);
  }
};

const compareNutritionData = (mainNutritionResponseData) => {
  displayNutritionalDataList.value = [];
  displayNutritionalDataList.value = nutritionDataList.value.map((value) => ({
    nutrientName: value.name,
    subNutrientName: !!value && value.isSubNutrient,
    unitKey: value?.unit?.key || "",
    valueUnit: value?.unit?.abbreviation ?? "",
    hasDvp: !!value && value.hasDvp,
    isHeader: !!value && value.isHeader,
    valueAmount: "",
    unit: value?.key ?? "",
    itemName: value?.key ?? "",
    nutrientUnit: "",
    dvpValue: "",
  }));


  displayNutritionalDataList.value.forEach((data) => {
    if (mainNutritionResponseData.length !== 0) {
      mainNutritionResponseData?.forEach((item) => {
        if (data.unit == item.name) {
          data.valueAmount = item?.amount?.value
            ? String(item.amount.value).length >= 9
              ? String(item.amount.value).slice(0, 9)
              : item.amount.value
            : "";
          data.unitKey = item?.amount?.unit ?? "";
          data.nutrientUnit = item?.amount?.unit ?? "";
          data.itemName = item?.name ?? "";
          data.dvpValue = item?.dvp?.value ?? "";
        }
      });
    }
  });


  displayNutritionalDataList.value.forEach((data) => {
    nutrientUnitsConstants.value.forEach((item) => {
      if (data.unitKey == item.key) {
        data.valueUnit = item.abbreviation ?? "";
      }
    });
  });
};

const getNutritionTable = (recipeData) => {
  const dataNutrient = [recipeData.nutrientTable];
  dataNutrient.forEach((data) => {
    if (data?.[lang.value]) {
      const langData = data[lang.value];
      isNutritionDropDownIconVisible.value = !!(
        langData.perServing && langData.per100g
      );

      if (langData.per100g) {
        selectedNutritionType.value = "per100g";
        compareNutritionData(langData.per100g);
        nutrientTableData.value['per100g'] = [...displayNutritionalDataList.value];
      }
      if (langData.perServing) {
        selectedNutritionType.value = "perServing";
        compareNutritionData(langData.perServing);
        nutrientTableData.value['perServing'] = [...displayNutritionalDataList.value];
      }
    }
  });
};

const chooseRecipeVariant = (language) => {
  isDataLoading.value = true;
  recipePreviewVariantSelectedLanguage.value = language;
  isVariantDropdownVisible.value = false;
  lang.value = language;
  duplicateTask.value = [];
  duplicateIngredient.children = [];
  categoriesList.value = [];
  selectedDiets.value = [];
  selectedAllergens.value = [];
  showImageVideo("image");
  recipeAttributionAuthor.value = "";
  recipeAttributionExternalId.value = "";
  servings.value = "";
  selectedServingsName.value = "";

  if (language === defaultLang) {
    getRecipeAsync(recipeIsin);
  } else {
    getEditRecipeDataAsync(recipeData.value);
  }
};

const recipeVariantData = (data) => {
  if (data) {
    varaiantdropdowndata.value = [...data];
    if (varaiantdropdowndata.value.length > 1) {
      isVariantExist.value = true;
      varaiantdropdowndata.value.forEach((lang) => {
        let langData = {
          language: lang,
          default_check: lang === defaultLang,
          language_name:
            lang === "es-US"
              ? "Spanish"
              : lang === "fr-FR"
              ? "French"
              : "English",
          languageFlag: `/images/flags/${
            lang === "es-US" ? "spain" : lang === "fr-FR" ? "france" : "us"
          }-flag.png`,
        };
        recipePreviewVariantLanguageList.value.push(langData);
      });
    } else {
      isVariantExist.value = false;
    }
  }
};

const openVariantDropdown = () => {
  isVariantDropdownVisible.value = !isVariantDropdownVisible.value;
};

const videoPopup = () => {
  videoLink.value = productVideo.value;
  isVideoModalVisible.value = true;
  props.checkRecipePreviewVideo?.(isVideoModalVisible.value);
};

const openRecipeStepVideoPopup = (data) => {
  videoLink.value = data;
  isVideoModalVisible.value = true;
  props.checkRecipePreviewVideo?.(isVideoModalVisible.value);
};

const handleClickOutside = (event) => {
  if (
    isVariantDropdownVisible.value &&
    !document
      .querySelector("#recipe-preview-variant-dropdown")
      .contains(event.target)
  ) {
    isVariantDropdownVisible.value = false;
  }
  if (
    isServingsDropdownResultVisible.value &&
    !document.querySelector("#servings-drop-down").contains(event.target)
  ) {
    isServingsDropdownResultVisible.value = false;
  }
  if (
    isNutritionDropdownResultVisible.value &&
    !document.querySelector("#preview-nutrition-drop-down").contains(event.target)
  ) {
    isNutritionDropdownResultVisible.value = false;
  }
};

const closeModal = () => {
  isVideoModalVisible.value = false;
  isRecipeStepVideoOpen.value = false;
  props.checkRecipePreviewVideo?.(false);
};

const onMute = () => {
  const video = getRef("video-container-popup");
  if (!video.muted) {
    isVideoMuted.value = true;
    video.muted = true;
  } else {
    isVideoMuted.value = false;
    video.muted = false;
  }
};

const stepVideoOnMute = () => {
  const video = getRef("stepVideoContainerPopup");
  if (!video.muted) {
    isStepVideoMuted.value = true;
    video.muted = true;
  } else {
    isStepVideoMuted.value = false;
    video.muted = false;
  }
};

const save = () => {
  if (recipeData.value && recipeData.value.state === "publishing") {
    return;
  }
  if (recipeIsin.value !== "") {
    router.push({
      path: "recipe-detail",
      query: { isin: recipeIsin.value },
    });
  }
};

const getRecipeAsync = async (id) => {
  const params = {
    country: lang.value.split("-")[1],
  };
  isDataLoading.value = true;
  try {
    await store.dispatch("ingredient/getRecipeAsync", {
      params,
      isin: id,
    });
    const response = store.getters["ingredient/getRecipeList"];
    recipePrice.value = response?.price?.value >= 0 ? response.price.value : "";
    recipeCurrency.value = response?.price?.currency === "usd" ? "Dollars" : "";

    if (response?.langs && !isVariantExist.value) {
      recipeVariantData(response.langs);
    }


    recipeData.value = response;
    await getEditRecipeDataAsync(recipeData.value);
    displayPublishButton(recipeData.value.state);
    nutritionKey.value = response?.nutrientTable?.[lang.value] ?? {};
  } catch {
    isDataLoading.value = false;
  }
};

// Back button confirmation
const backButtonConfirm = () => {
  const editRecipeQuery = route.query.editRecipeQuery || "";

  if (route.query.from !== "1" && editRecipeQuery) {
    router.push({
      path: "/recipes",
      query: {
        page: route.query.from,
        editRecipeQuery,
      },
    });
  } else if (editRecipeQuery) {
    router.push({
      path: "/recipes",
      query: { editRecipeQuery },
    });
  } else {
    router.go(-1);
  }
};

const toggleMute = () => {
  const video = getRef("video-container-popup");
  isVideoMuted.value = !isVideoMuted.value;
  video.muted = isVideoMuted.value;
};

// Fetch organizations list
const getOrganizationsListAsync = async () => {
  const payload = {
    from: 0,
    size: 100,
  };

  try {
    await store.dispatch("organizations/getSearchOrganizationsAsync", {
      payload,
      lang: lang.value,
    });

    const response = store.getters["organizations/getOrganizationsData"];
    publisherDataList.value = response?.data?.results || [];

    if (publisherDataList.value.length) {
      const selectedPublisher = publisherDataList.value.find(
        (data) => data.isin === recipeAttributionOrganization.value
      );
      if (selectedPublisher) {
        selectedPublisherName.value = selectedPublisher.name || "";
        selectedPublisherImage.value =
          selectedPublisher.image?.url || defaultOrganizationsImage;
      }
    }
  } catch (error) {
    isDataLoading.value = false;
    console.error("Error fetching organizations list", error);
  }
};

// Open external link
const openLinkPage = () => {
  window.open(urlLinkUpdate.value, "_blank");
};

const openImageLinkPage = () => {
  window.open(imageUrlLinkUpdate.value, "_blank");
};

const getEditRecipeDataAsync = async (recipeDataParam) => {
  isDataLoading.value = false;
  emit($keys.KEY_NAMES.LOADER_PROGRESS, true);

  handleRecipeBasics(recipeDataParam);
  handleTimeData(recipeDataParam.time);
  handleAttributionAndMedia(recipeDataParam);
  handleDietsTagsCategories(recipeDataParam);
  getNutritionTable(recipeDataParam);
  handleTasks(recipeDataParam);
  handleNotesAndServings(recipeDataParam);
  handleIngredientsUnits();
  isVideoPresent.value = Boolean(recipeDataParam?.media?.[lang.value]?.video?.[0]?.url);
};

const handleRecipeBasics = (recipeData) => {
  const {
    title,
    subtitle,
    yield: { serving, raw, servingSize, servingsPerContainer } = {},
  } = recipeData || {};

  recipeName.value = title?.[lang.value] || "";
  recipeSubtitle.value = subtitle?.[lang.value] || "";
  servings.value = serving || "";
  yieldData.value = raw || "";
  nutritionServingSize.value = servingSize || "";
  nutritionServingSizePerContainer.value = servingsPerContainer || "";
};

const handleTimeData = (timeData) => {
  if (!timeData) return;
  getTotalTime(timeData.total ?? getTotalTime(timeData.total));
  getPreparationTime(timeData.prep ?? getPreparationTime(timeData.prep));
  getCookTime(timeData.cook ?? getCookTime(timeData.cook));
};

const handleAttributionAndMedia = (recipeData) => {
  const {
    media,
    attribution,
    description: recipeDescription,
    slug,
    externalId,
  } = recipeData || {};

  recipeAttributionAuthor.value =
    attribution?.[lang.value]?.author?.name ?? "";
  recipeAttributionExternalId.value = externalId ?? "";
  recipeAttributionOrganization.value = attribution?.[lang.value]?.organization ?? "";
  productImage.value = media?.[lang.value]?.image ?? "";
  description.value = recipeDescription?.[lang.value]?.abstract ?? "";
  slugEdit.value = slug?.[lang.value] ?? "";
  productVideo.value = media?.[lang.value]?.video?.[0]?.url ?? "";
  videoDimension.value = media?.[lang.value]?.video?.[0]?.size ?? "";
  imageUrlLinkUpdate.value = media?.[lang.value]?.externalImageUrl ?? "";
  urlLinkUpdate.value = media?.[lang.value]?.externalVideoUrl ?? "";

  handleImageUrlDomain();
  handleUrlLinkUpdate();
};

const handleImageUrlDomain = () => {
  if (imageUrlLinkUpdate.value) {
    try {
      imageUrlDomain.value =
        new URL(imageUrlLinkUpdate.value)?.hostname?.toUpperCase() || "";
    } catch (error) {
      console.error(error);
      imageUrlDomain.value = "";
    }
  }
};

const handleUrlLinkUpdate = () => {
  if (urlLinkUpdate.value) {
    try {
      const url = new URL(urlLinkUpdate.value);
      hostNameUrlLink.value = url.hostname ?? "";

      if (
        urlLinkUpdate.value.includes("youtube.com") &&
        url.searchParams.get("v")
      ) {
        linkURLImage.value = `http://img.youtube.com/vi/${url.searchParams.get(
          "v"
        )}/hqdefault.jpg`;
      } else {
        linkURLImage.value = emptyVideoImage;
      }

      hostNameUrlLinkUpdate.value = hostNameUrlLink.value.toUpperCase();
      isLinkPresent.value = true;
    } catch (error) {
      console.error(error);
      urlLinkUpdate.value = "";
      isLinkPresent.value = false;
    }
  }
};

const handleDietsTagsCategories = (recipeData) => {
  diets.value = recipeData.diets;
  getDiets(recipeData);
  getTagsAsync(recipeData);
  getCategoriesAsync(recipeData);
  getAllergens(recipeData);
};
const handleTasks = (recipeData) => {
  if (recipeData.provider === store.getters["KEY_NAMES/FIMS"]) {
    recipeData.tasks.forEach((item) => (item.isChecked = true));
  } else {
    recipeData.tasks.forEach((item) => (item.isChecked = false));
  }

  recipeData.tasks.forEach((data) => {
    if (data?.[lang.value]?.ingredients) {
      data[lang.value].ingredients = data[lang.value].ingredients.map((ingredientsData) => {
        const {
          name = "",
          amount: { value: quantity = 0, unit: UOM = "" } = {},
          rawText = "",
          foodItem = "",
          group = "",
          excludeFromNutrition = false,
          level = "main",
          modifier = "",
          note = "",
          keywords = [],
          keywordInput = "",
        } = ingredientsData;

        return {
          name,
          quantity,
          UOM,
          rawText,
          uomAutocomplete: false,
          foodItem,
          group,
          excludeFromNutrition,
          level,
          modifier,
          note,
          keywords,
          keywordInput,
        };
      });
    } else {
      data[lang.value].ingredients = [];
    }
  });

  tasksData.value = recipeData.tasks.map(
    ({ isChecked, [lang.value]: { title, instructions, ingredients, media } }) => ({
      isChecked: isChecked ?? false,
      [lang.value]: {
        title: title ?? "",
        instructions: instructions ?? [],
        ingredients: ingredients ?? [],
        media: {
          image: media?.image ?? "",
          video: [
            {
              url: media?.video?.[0]?.url ?? "",
            },
          ],
        },
      },
    })
  );

  duplicateTask.value = recipeData.tasks.map(
    ({ [lang.value]: { title, instructions, ingredients, media } }) => ({
      [lang.value]: {
        duplicateTitle: title ?? "",
        duplicateInstructions: instructions ?? [],
        duplicateIngredients: ingredients ?? [],
        duplicateMedia: {
          image: media?.image ?? "",
          video: [
            {
              url: media?.video?.[0]?.url ?? "",
            },
          ],
        },
      },
    })
  );
};

const handleIngredientsUnits = () => {
  tasksData.value.forEach((data) => {
    if (data?.[lang.value]?.ingredients) {
      data[lang.value].ingredients.forEach((ingredientsData) => {
        const foundElement = nutrientUnitsConstants.value.find(
          (element) => ingredientsData.UOM === element.key
        );
        ingredientsData.UOM = foundElement ? foundElement.display : "";
      });
    }
  });
};

const handleNotesAndServings = (recipeData) => {
  const recipeTasks = recipeData?.ingredients?.[lang.value] ?? [];
  if (recipeTasks.length) {
    getUpdatedIngredientDataAsync(recipeTasks);
  }

  notes.value = recipeData?.description?.[lang.value]?.notes ?? "";
  availableServings.value = recipeData.yield?.availableServings ?? [];
};

const getTagsAsync = async (recipeData) => {
  const tags = recipeData?.tags?.[lang.value] || [];
  const payload = {
    from: 0,
    size: 15,
    type: $keys.KEY_NAMES.TAG,
    isins: tags.join(","),
  };

  if (tags.length) {
    isDataLoading.value = true;
    try {
      await store.dispatch(
        "categoriesGroup/getCategoryForCategoryGroupListAsync",
        { payload }
      );
      const response =
        store.getters["categoriesGroup/getCategoryForCategoryGroupList"];
      selectedTags.value = response.results;
    } catch (error) {
      console.error($keys.KEY_NAMES.ERROR_IN + "getTagsAsync", error);
    } finally {
      isDataLoading.value = false;
    }
  }
};

const getDiets = (recipeData) => {
  const dietsData = recipeData?.diets || [];
  if (dietsData.length) {
    dietsData.forEach((data) => {
      dietsList.value.forEach((item) => {
        if (data === item.key) {
          selectedDiets.value.push(item);
        }
      });
    });
  }
};

const getCategoriesAsync = async (recipeData) => {
  const categoriesData = recipeData?.categories?.[lang.value] || [];
  if (categoriesData.length) {
    const payload = {
      from: 0,
      size: 15,
      type: t('COMMON.CATEGORY'),
      isins: categoriesData.join(","),
    };

    isDataLoading.value = true;
    try {
      await store.dispatch(
        "categoriesGroup/getCategoryForCategoryGroupListAsync",
        { payload }
      );
      const response =
        store.getters["categoriesGroup/getCategoryForCategoryGroupList"];
      if (response?.results) {
        response.results.forEach((data) => {
          const item = categoriesList?.value?.find((item) => data.isin === item.isin);
          if (item) {
            item.isAlreadyInSelectedCategory = true;
          }
        });
        selectedCategories.value = response.results;
      }
    } catch (error) {
      console.error($keys.KEY_NAMES.ERROR_IN + "getCategoriesAsync", error);
    } finally {
      isDataLoading.value = false;
    }
  }
};

// Get Recipe Diets
const getRecipeDietsAsync = async () => {
  try {
    await store.dispatch("recipe/getRecipeDietsAsync", {
      query: "",
      recipeCount: false,
      lang: lang.value,
    });
    const response = store.getters["recipe/getRecipeDiets"];
    dietsList.value = response?.results ?? [];
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getRecipeDietsAsync", error);
  }
};

// Get Allergens
const getAllergens = (recipeData) => {
  const allergensData = recipeData?.labels || [];
  if (allergensData.length) {
    allergensData.forEach((data) => {
      allergensList.value.forEach((item) => {
        if (data === item.key) {
          selectedAllergens.value.push(item);
        }
      });
    });
  }
};

// Get Recipe Allergens
const getRecipeAllergensAsync = async () => {
  try {
    await store.dispatch("recipeDetails/getRecipeAllergensAsync", {
      query: "",
      lang: lang.value,
    });
    const response = store.getters["recipeDetails/getRecipeAllergens"];
    allergensList.value = response?.results ?? [];
  } catch (error) {
    console.error($keys.KEY_NAMES.ERROR_IN + "getRecipeAllergensAsync", error);
  }
};

const createEmptyGroup = () => ({
  children: [],
  displayGroup: false,
  name: "",
});

const getUpdatedIngredientDataAsync = async (data) => {
  const totalIngredients = [];
  const ingredientsIsinList = [];

  data.forEach((ingredientsData) => {
    const ingredient = {
      name: ingredientsData?.name ?? "",
      quantity: ingredientsData?.amount?.value ?? 0,
      UOM: ingredientsData?.amount?.unit ?? "",
      rawText: ingredientsData?.rawText ?? "",
      foodItem: ingredientsData?.foodItem ?? "",
      uomAutocomplete: false,
      group: ingredientsData.group || "",
      excludeFromNutrition: ingredientsData.excludeFromNutrition || false,
      level: ingredientsData.level || "main",
      modifier: ingredientsData.modifier || "",
      note: ingredientsData.note || "",
      keywords: ingredientsData.keywords || [],
      keywordInput: ingredientsData.keywordInput || "",
      hasOverridingCampaign: false,
    };
    totalIngredients.push(ingredient);
    if (ingredient.foodItem) {
      ingredientsIsinList.push(ingredient.foodItem);
    }
  });

  ingredientsData.value = groupData(totalIngredients);

  ingredientsData.value.children.forEach((data) => {
    data.displayGroup = data.name !== "";
  });

  if (
    ingredientsData.value.children.length > 0 &&
    ingredientsData.value.children[0].name !== ""
  ) {
    ingredientsData.value.children.unshift(createEmptyGroup());
  }

  ingredientsData.value.children.forEach((data, groupIndex) => {
    if (data.children?.length) {
      data.children.forEach((item, index) => {
        item.id = `oldId${groupIndex}${index}`;
        item.isChecked = false;
        const foundElement = nutrientUnitsConstants.value.find(
          (element) => item.UOM === element.key
        );
        item.UOM = foundElement ? foundElement.display : "";
      });
    }
  });

  ingredientsData.value.children.forEach((data) => {
    if (data.children.length > 0) {
      duplicateIngredient.children.push({
        children: data.children,
        name: data.name || "",
        displayGroup: data.displayGroup || false,
      });
    }
  });
  processTasksData();
  let idxForID = 0;
  duplicateIngredient.children.forEach((data) => {
    if (data.children?.length) {
      data.children = data.children.map((item) => ({
        duplicateId: idxForID++,
        duplicateName: item.name,
        duplicateQuantity: item.quantity,
        duplicateUOM: item.UOM,
        note: item.note || "",
        foodItem: item.foodItem || "",
      }));
    }
  });

  let indexForID = 0;
  duplicateTask.value.forEach((task) => {
    if (task?.[lang.value]?.duplicateIngredients) {
      task[lang.value].duplicateIngredients.forEach((item) => {
        item.duplicateId = indexForID++;
      });
    }
  });

  if (ingredientsIsinList.length > 0 && recipe.value.provider === "fims") {
    foodItemName(ingredientsIsinList);
  }

};
const processTasksData = (tasksData) => {
  if (tasksData?.length) {
    tasksData.forEach((taskData) => {
      if (taskData?.[lang.value]?.ingredients?.length) {
        processIngredients(taskData[lang.value].ingredients);
      }
    });
  }
};

// Process individual ingredients
const processIngredients = (ingredients) => {
  ingredients.forEach((ingredient) => {
    updateItemId(ingredient);
  });
};

const updateItemId = (ingredient) => {
  if (ingredientsData.value?.children?.length) {
    ingredientsData.value.children.forEach((data) => {
      if (data?.children?.length) {
        checkItemMatch(data.children, ingredient);
      }
    });
  }
};

// Check if the item matches the ingredient
const checkItemMatch = (taskItems, ingredient) => {
  taskItems.forEach((taskItem) => {
    if (isMatchingItem(taskItem, ingredient)) {
      ingredient.id = taskItem.id;
    }
  });
};

// Define the matching criteria for items
const isMatchingItem = (taskItem, ingredient) => {
  return (
    ingredient.rawText === taskItem.rawText &&
    ingredient.name === taskItem.name &&
    ingredient.quantity === taskItem.quantity &&
    ingredient.UOM === taskItem.UOM
  );
};

// Fetch food item names
const foodItemName = async (dataArray) => {
  isDataLoading.value = true;
  try {
    await FoodItemService.getAllFoodItemIsin(
      project.value,
      dataArray,
      lang.value,
      store
    );
    updateIngredientNames();
    updateDuplicateIngredientNames();
    updateDuplicateTaskNames();
    processTaskData();
  } catch (error) {
    console.error("Error fetching food item names:", error);
  } finally {
    isDataLoading.value = false;
  }
};

// Update ingredient names based on fetched data
const updateIngredientNames = () => {
  if (ingredientsData.value?.children?.length) {
    ingredientsData.value.children.forEach((data) => {
      processChildData(data);
    });
  }
};

// Process child data for updates
const processChildData = (data) => {
  if (data?.children?.length) {
    updateNamesInChildren(data.children);
  }
};

const updateNamesInChildren = (children) => {
  if (children?.length) {
    children.forEach((item) => {
      updateItemName(item);
    });
  }
};
const updateItemName = (item) => {
  const nameData = findNameData(item?.foodItem);
  if (nameData) {
    item.name = nameData?.name?.singular ?? "";
  }
};

const findNameData = (foodItem) => {
  return dataOfName.value.find((namedata) => namedata?.isin === foodItem);
};

// Update duplicate ingredient names
const updateDuplicateIngredientNames = () => {
  if (duplicateIngredient?.children?.length) {
    duplicateIngredient.children.forEach(processDuplicateChildData);
  }
};

// Process duplicate child data
const processDuplicateChildData = (data) => {
  if (data?.children?.length) {
    updateDuplicateNamesInChildren(data.children);
  }
};

// Update duplicate names in children
const updateDuplicateNamesInChildren = (children) => {
  children.forEach(updateDuplicateItemName);
};

// Update duplicate item name
const updateDuplicateItemName = (item) => {
  const nameData = findDuplicateNameData(item?.foodItem);
  if (nameData) {
    item.duplicateName = nameData?.name?.singular ?? "";
  }
};

// Find duplicate name data based on food item
const findDuplicateNameData = (foodItem) => {
  return dataOfName.value.find((nameData) => nameData?.isin === foodItem);
};

// Update duplicate task names
const updateDuplicateTaskNames = () => {
  if (duplicateTask.value?.length) {
    duplicateTask.value.forEach((task) => {
      if (task?.[duplicateTask]?.duplicateIngredients?.length) {
        updateIngredientsName(task[lang.value].duplicateIngredients);
      }
    });
  }
};

// Update ingredient names in the given array
const updateIngredientsName = (ingredients) => {
  ingredients.forEach(assignNameToIngredient);
};

const assignNameToIngredient = (ingredient) => {
  const nameData = getNameData(ingredient?.foodItem);
  if (nameData) {
    ingredient.name = nameData?.name?.singular ?? "";
  }
};

// Get name data for the food item
const getNameData = (foodItem) => {
  return dataOfName.value.find((item) => item?.isin === foodItem);
};

// Process task data
const processTaskData = () => {
  if (tasksData.value?.length) {
    tasksData.value.forEach((task) => {
      if (task?.[lang.value]?.ingredients?.length) {
        handleIngredients(task[lang.value].ingredients);
      }
    });
  }
};

const handleIngredients = (ingredients) => {
  ingredients.forEach(assignIngredientName);
};

const assignIngredientName = (ingredient) => {
  if (dataOfName.value?.length) {
    dataOfName.value.forEach((nameData) => {
      if (nameData?.isin === ingredient?.foodItem) {
        ingredient.name = nameData?.name?.singular ?? "";
      }
    });
  }
};

const groupData = (itemsList) => {
  if (!itemsList?.length) return { name: "ChildrenArray", children: [] };

  const groupedEntries = Object.entries(
    itemsList.reduce((groupedItems, currentItem) => {
      if (!groupedItems[currentItem.group]) {
        groupedItems[currentItem.group] = [];
      }
      groupedItems[currentItem.group].push(currentItem);
      return groupedItems;
    }, {})
  );

  return groupedEntries.reduce(
    (result, currentGroup) => {
      result.children.push({
        name: currentGroup[0],
        children: currentGroup[1],
      });
      return result;
    },
    { name: "ChildrenArray", children: [] }
  );
};

const formatTime = (hour, minute, second = "") => {
  if (hour) {
    return minute
      ? `${hour}.${String(minute).padStart(2, "0")} hour`
      : `${hour} hour`;
  } else if (minute) {
    return `${minute} min`;
  }
  return "";
};

const getTotalTime = (time) => {
  totalTime.value = "";
  if (!time) return;
  const { hour: h, minute: m, second: s } = extractTimeParts(time);
  hour.value = h;
  minute.value = m;
  second.value = s === 0 ? "" : s;
  totalTime.value = formatTime(h, m, s);
};

const getPreparationTime = (time) => {
  if (!time) {
    preparationTime.value = "";
    return;
  }
  const { hour: prepHour, minute: prepMinute } = extractTimeParts(time);
  preparationTime.value = formatTime(prepHour, prepMinute);
};

const getCookTime = (time) => {
  if (!time) {
    cookingTime.value = "";
    return;
  }
  const { hour: cookHour, minute: cookMinute } = extractTimeParts(time);
  cookingTime.value = formatTime(cookHour, cookMinute);
};

// Show/hide image or video
const showImageVideo = (file) => {
  let image = getRef("imageToggle");
  let video = getRef("videoToggle");

  if (file == "image") {
    image.style.backgroundColor = "#4DB935";
    image.style.color = "#FFFFFF";
    video.style.backgroundColor = "#FFFFFF";
    video.style.color = "#A8ABB0";
    imageToggle.style.width = "125px";
    imageToggle.style.zIndex = 10;
    videoToggle.style.zIndex = 5;
    videoToggle.style.width = "105px";
    isImageVisible.value = true;
    isVideoVisible.value = false;
  } else {
    video.style.backgroundColor = "#4DB935";
    video.style.color = "#FFFFFF";
    image.style.backgroundColor = "#FFFFFF";
    image.style.color = "#A8ABB0";
    isVideoVisible.value = true;
    isImageVisible.value = false;
    imageToggle.style.width = "105px";
    videoToggle.style.width = "125px";
    videoToggle.style.marginLeft = "-19px";
    videoToggle.style.zIndex = 10;
  }
};

// Asynchronous function for selecting serving product
const selectedServingsProductAsync = async (size) => {
  selectedServingsName.value = size;
  isServingsDropdownResultVisible.value = false;
  isIngredientsDataLoading.value = true;
  isStepIngredientsDataLoading.value = true;

  updateIngredientUnits(tasksData.value);
  updateIngredientUnits(ingredientsData.value.children);

  await getServingScaleAsync(size);
  await getTaskServingScaleAsync(size);
};

// Update ingredient units based on nutrient constants
const updateIngredientUnits = (data) => {
  if (!data || !data.length) return;

  data.forEach((item) => {
    if (item?.name) {
      nutrientUnitsConstants.value.forEach((uom) => {
        if (item.UOM == uom.display) {
          item.UOM = uom.key;
        }
      });
    }

    if (item?.children) {
      updateIngredientUnits(item.children);
    }
  });
};

// Get ingredient name
const getIngredientName = (responseItem, duplicateItem) => {
  return responseItem.name || duplicateItem.name;
};
const getIngredientUOM = (responseItem, duplicateItem) => {
  return responseItem.amount?.unit?.key || duplicateItem.UOM;
};

// Get ingredient quantity
const getIngredientQuantity = (responseItem, duplicateItem) => {
  return (
    Math.round((responseItem.amount?.value || duplicateItem.quantity) * 100) /
    100
  );
};

// Asynchronous function to get task serving scale
const getTaskServingScaleAsync = async (size) => {
  let dataIngredient = [];
  console.log("tasksData.value", lang.value, tasksData.value);

  tasksData.value.forEach((task) => {
    const ingredients = task?.[lang.value]?.ingredients;
    ingredients?.forEach((item) => {
      if (item?.name) {
        dataIngredient.push({
          amount: {
            value: item.quantity ? Number(item.quantity) : 0,
            unit: item.UOM ?? "",
          },
          name: item.name,
        });
      }
    });
  });

  const payload = {
    scaleFactor: size && servings.value ? Number(size / servings.value) : 0,
    ingredients: dataIngredient,
  };

  try {
    await store.dispatch("recipePreview/getRecipeServingScaleAsync", {
      payload,
      lang: lang.value,
    });
    const response = store.getters["recipePreview/getRecipeServingScale"];

    duplicateTask.value.forEach((duplicateTaskItem) => {
      console.log("duplicateTask", duplicateTaskItem);
      let updatedData = [];
      const duplicateIngredients =
        duplicateTaskItem?.[lang.value]?.duplicateIngredients;

      duplicateIngredients?.forEach((item) => {
        response?.forEach((res, index) => {
          if (Number(item?.duplicateId) === index) {
            updatedData.push({
              nameMirror: res.nameMirror || item.nameMirror,
              UOMMirror: res.UOMMirror || item.UOMMirror,
              quantityMirror: res.amount?.value
                ? Math.round(res.amount.value * 100) / 100
                : Math.round(item.quantityMirror * 100) / 100,
              foodItem: item.foodItem || "",
              modifier: item.modifier || "",
              note: item.note || "",
              duplicateId: item.duplicateId || "",
            });
          }
        });
      });

      duplicateTaskItem[lang.value].duplicateIngredients = updatedData;
    });

    duplicateTask.value.forEach((duplicateTaskItem) => {
      const duplicateIngredients =
        duplicateTaskItem?.[lang.value]?.duplicateIngredients;
      duplicateIngredients?.forEach((item) => {
        ingredientsUomList.value?.forEach((uom) => {
          if (item.UOM === uom.key) {
            item.UOM = uom.display;
          }
        });
      });
    });

    applyNutrientUnits();
  } catch (error) {
    console.error(
      `${$keys.KEY_NAMES.ERROR_IN} getTaskServingScaleAsync:`,
      error
    );
    isDataLoading.value = false;
  } finally {
    isStepIngredientsDataLoading.value = false;
  }
};



// Apply nutrient units to the duplicate tasks
const applyNutrientUnits = () => {
  duplicateTask.value.forEach((duplicate) => {
    const langData = duplicate[lang.value];
    if (langData?.duplicateIngredients) {
      langData.duplicateIngredients.forEach((item) => {
        nutrientUnitsConstants.value.forEach((uom) => {
          if (item?.UOM === uom?.key) {
            item.UOM = uom.display;
          }
        });
      });
    }
  });
};

// Asynchronous function to get serving scale
const getServingScaleAsync = async (size) => {
  const dataIngredient = collectIngredients();

  const payload = {
    scaleFactor: size && servings.value ? Number(size / servings.value) : 0,
    ingredients: dataIngredient,
  };

  try {
    isIngredientsDataLoading.value = true; // Start loading state
    await store.dispatch("recipePreview/getRecipeServingScaleAsync", {
      payload,
      lang: lang.value,
    });

    const response = store.getters["recipePreview/getRecipeServingScale"];
    updateDuplicateIngredients(response);
    updateDuplicateUOMs();
  } catch (error) {
    console.error("Error fetching serving scale:", error);
  } finally {
    isIngredientsDataLoading.value = false; // End loading state
    if (typeof $nuxt !== "undefined") {
      $nuxt.$loading.finish();
    }
  }
};

// Collect ingredients data from the ingredientsData
const collectIngredients = () => {
  const dataIngredient = [];

  if (ingredientsData.value?.children?.length) {
    ingredientsData.value.children.forEach((data) => {
      if (data.children?.length) {
        data.children.forEach((item) => {
          if (item?.name) {
            dataIngredient.push({
              amount: {
                value: item.quantity ? Number(item.quantity) : 0,
                unit: item.UOM || "",
              },
              name: item.name,
            });
          }
        });
      }
    });
  }
  return dataIngredient;
};

// Update duplicate ingredients with new data from the response
const updateDuplicateIngredients = (response) => {
  if (duplicateIngredient?.children) {
    duplicateIngredient.children.forEach((data) => {
      if (data.children?.length) {
        const updatedData = data.children.map((item) => {
          const matchedResponse = response?.find(
            (res, index) => item.duplicateId == index
          );
          if (matchedResponse) {
            return {
              duplicateName: matchedResponse.name || item.duplicateName,
              duplicateUOM:
                matchedResponse.amount?.unit?.display || item.duplicateUOM,
              duplicateQuantity: matchedResponse.amount?.value
                ? Math.round(matchedResponse.amount.value * 100) / 100
                : Math.round(item.duplicateQuantity * 100) / 100,
              note: item.note || "",
              foodItem: item.foodItem || "",
              duplicateId: item.duplicateId || "",
            };
          }
          return item;
        });
        data.children = updatedData;
      }
    });
  }
};
const updateDuplicateUOMs = () => {
  if (Array.isArray(duplicateIngredient)) {
    duplicateIngredient.forEach((data) => {
      if (data.children?.length) {
        data.children.forEach((item) => {
          if (Array.isArray(nutrientUnitsConstants.value)) {
            const uom = nutrientUnitsConstants.value.find(
              (uom) => uom.key === item.duplicateUOM
            );
            if (uom) {
              item.duplicateUOM = uom.display;
            }
          }
        });
      }
    });
  }
};

// Toggle visibility of servings dropdown
const showServingsData = () => {
  isServingsDropdownResultVisible.value =
    !isServingsDropdownResultVisible.value;
};

// Fetch feature configuration
const getFeatureConfigAsync = async () => {
  const params = { lang: lang.value };

  try {
    await store.dispatch("ingredient/getFeatureConfigAsync", { params });
    const response = store.getters["ingredient/getFeatureConfig"];
    isShowPublisher.value = response?.organizations ?? false;
  } catch (error) {
    console.error("Error in getFeatureConfigAsync:", error);
  }
};
</script>
