<template>
  <div
    ref="selectDropdownElRef"
    class="select-dropdown font-family-averta"
    :class="{
      'select-dropdown-open': isOpen,
    }"
    :aria-label="ariaLabel"
    :aria-expanded="isOpen"
    :data-test-id="testId"
  >
    <button
      type="button"
      class="btn-reset select-dropdown-button w-100 h-100 font-size-base font-normal line-height-20 color-graphite-gray"
      @click="toggleDropdown"
      :data-test-id="dropdownButtonTestId"
    >
      <span class="text-overflow">{{ model.label }}</span>
      <IconArrowDownDefault class="select-dropdown-button-icon" />
    </button>

    <Teleport to="body">
      <div
        v-if="isOpen"
        ref="selectDropdownPanelElRef"
        class="select-dropdown-panel font-family-averta"
        :class="[panelClasses]"
        :style="dropdownPanelStyles"
        v-on-click-outside="onClickOutsideHandler"
      >
        <div class="select-dropdown-panel-search w-100">
          <img alt="search" src="@/assets/images/Search_Bar_Icon-web.png" />
          <input
            type="text"
            name="select-dropdown-panel-search"
            v-model.trim="searchInputModel"
            class="font-size-base font-normal"
            :placeholder="searchInputPlaceholder"
            :data-test-id="searchInputTestId"
          >
          <button
            v-show="searchInputModel"
            type="button"
            class="btn-reset"
            @click="clearSearchInputModel()"
            :data-test-id="searchInputClearTestId"
          >
            <img alt="exit" src="@/assets/images/exit-gray.png" />
          </button>
        </div>
        <div class="select-dropdown-panel-list iq-scrollbar-small">
          <button
            v-for="(item, index) in filteredOptions"
            :key="index"
            :id="item.id"
            type="button"
            class="select-dropdown-panel-list-item btn-reset w-100 font-size-base font-normal color-black"
            :class="{
              'select-dropdown-panel-list-item-active': item.id === model.id,
            }"
            @click="selectOption(item)"
          >
            {{ item.label }}
          </button>
        </div>
      </div>
    </Teleport>
  </div>
</template>

<script setup>
import { computed, ref } from "vue";
import { vOnClickOutside } from '@vueuse/components';
import IconArrowDownDefault from '~/assets/images/icons/arrow-down-default.svg';

const props = defineProps({
  options: {
    type: Array,
    default: () => [], // [{id: "", label: ""}]
  },
  ariaLabel: {
    type: String,
    default: () => "Select dropdown",
  },
  testId: {
    type: String,
    default: () => undefined,
  },
  dropdownButtonTestId: {
    type: String,
    default: () => undefined,
  },
  searchInputPlaceholder: {
    type: String,
    default: () => "Search",
  },
  searchInputTestId: {
    type: String,
    default: () => undefined,
  },
  searchInputClearTestId: {
    type: String,
    default: () => undefined,
  },
  panelClasses: {
    type: String,
    default: () => undefined,
  }
});

const selectDropdownElRef = ref();
const selectDropdownPanelElRef = ref();
const isOpen = ref(false);
const searchInputModel = ref("");

const model = defineModel({ default: { id: undefined, label: "" } });

const dropdownPanelStyles = reactive({
  position: 'fixed',
  top: '0px',
  left: '0px',
  width: '0px',
});

const filteredOptions = computed(() => {
  const searchValue = searchInputModel.value?.toLowerCase() ?? "";
  return searchValue ? props.options.filter((option) => option.label?.toLowerCase()?.startsWith(searchValue)) : props.options;
});

const setDropdownPosition = () => {
  const triggerEl = selectDropdownElRef.value;
  const dropdownEl = selectDropdownPanelElRef.value;
  if (!triggerEl || !dropdownEl) return;

  const rect = triggerEl.getBoundingClientRect();

  dropdownPanelStyles.top = `${Math.max(rect.bottom + 2, 0)}px`;
  dropdownPanelStyles.left = `${rect.left}px`;
  dropdownPanelStyles.width = `${rect.width}px`;
};

const clearSearchInputModel = () => searchInputModel.value = "";

const setOpen = (val) => isOpen.value = val;

const openDropdown = async () => {
  setOpen(true);
  await nextTick();
  setDropdownPosition();
};

const closeDropdown = () => {
  setOpen(false);
  clearSearchInputModel();
};

const toggleDropdown = () => !isOpen.value ? openDropdown() : closeDropdown();

const selectOption = (data) => {
  model.value = data;
  closeDropdown();
};

const onClickOutsideHandler = [() => closeDropdown(), { ignore: [selectDropdownElRef] }];

useEventListener('resize', setDropdownPosition);
useEventListener('scroll', setDropdownPosition);
onMounted(() => {
  setDropdownPosition();
});
</script>
