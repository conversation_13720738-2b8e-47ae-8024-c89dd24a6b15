export const useSearchStore = defineStore("Search", () => {
  const searchContexts = ref({
    global: {
      str: "",
      emitQueryParam: true,
    },
    'details-page': {
      str: "",
      emitQueryParam: false,
    }
  });

  const isSearchEnableRef = ref(false);
  const isSearchEnabledContexts = ref({
    global: false,
    'details-page': false
  });

  const searchQuery = computed(() => searchContexts.value.global);
  const isSearchEnabled = computed(() => isSearchEnableRef);

  const getSearchQuery = (context = 'global') => {
    return computed(() => searchContexts.value[context] || reactive({ str: "", emitQueryParam: true }));
  };

  const getIsSearchEnabled = (context = 'global') => {
    return computed(() => isSearchEnabledContexts.value[context] || false);
  };

  const setSearchQuery = (value, { emitQueryParam = true, context = 'global' } = {}) => {
    const trimmed = value?.trim() ?? '';

    if (!searchContexts.value[context]) {
      searchContexts.value[context] = reactive({ str: "", emitQueryParam: true });
    }

    searchContexts.value[context].str = trimmed;
    searchContexts.value[context].emitQueryParam = emitQueryParam;

    isSearchEnableRef.value = !!trimmed;
    isSearchEnabledContexts.value[context] = !!trimmed;
  };

  return {
    searchQuery,
    isSearchEnabled,
    setSearchQuery,
    getSearchQuery,
    getIsSearchEnabled,
  };
});
